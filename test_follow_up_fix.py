#!/usr/bin/env python3
"""
Test script to verify the follow-up question fix
Tests the consistency between stored and displayed follow-up questions
"""

import asyncio
import json
import logging
from datetime import datetime
from bson.objectid import ObjectId

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("FollowUpTest")

def test_follow_up_extraction():
    """Test the follow-up extraction functionality"""
    print("🧪 Testing Follow-up Question Extraction")
    print("=" * 50)
    
    # Import the memory manager
    from memory_manager import get_memory_manager
    memory_manager = get_memory_manager()
    
    # Test cases
    test_responses = [
        {
            "response": "Machine learning is a subset of artificial intelligence.\n\n👉 Would you like me to explain the different types of machine learning algorithms?",
            "expected_answer": "Machine learning is a subset of artificial intelligence.",
            "expected_follow_up": "Would you like me to explain the different types of machine learning algorithms?"
        },
        {
            "response": "Python is a programming language.\n\n👉 Shall I go into more detail about Python's features?",
            "expected_answer": "Python is a programming language.",
            "expected_follow_up": "Shall I go into more detail about Python's features?"
        },
        {
            "response": "This is a response without a follow-up question.",
            "expected_answer": "This is a response without a follow-up question.",
            "expected_follow_up": None
        }
    ]
    
    print(f"Testing {len(test_responses)} response scenarios...")
    
    for i, test_case in enumerate(test_responses, 1):
        print(f"\n🔍 Test {i}:")
        print(f"Input: {test_case['response'][:50]}...")
        
        # Extract follow-up
        answer, follow_up = memory_manager.extract_follow_up(test_case['response'])
        
        # Check results
        answer_correct = answer == test_case['expected_answer']
        follow_up_correct = follow_up == test_case['expected_follow_up']
        
        print(f"✅ Answer extraction: {'PASS' if answer_correct else 'FAIL'}")
        print(f"✅ Follow-up extraction: {'PASS' if follow_up_correct else 'FAIL'}")
        
        if not answer_correct:
            print(f"   Expected: {test_case['expected_answer']}")
            print(f"   Got: {answer}")
        
        if not follow_up_correct:
            print(f"   Expected: {test_case['expected_follow_up']}")
            print(f"   Got: {follow_up}")

async def test_generative_model_consistency():
    """Test that generative model returns consistent follow-up questions"""
    print("\n🤖 Testing Generative Model Follow-up Consistency")
    print("=" * 50)
    
    # Import the generative model
    from generative_model import get_generative_model
    generative_model = get_generative_model()
    
    # Test response with follow-up
    test_response = "Artificial intelligence is a broad field.\n\n👉 Would you like me to explain the different branches of AI?"
    
    # Validate the response
    validated_response = generative_model.validate_response(test_response, "agentic", ["AI context"])
    
    print(f"Original response: {test_response}")
    print(f"Validated response: {validated_response}")
    
    # Check if follow-up is preserved
    has_follow_up = "👉" in validated_response
    print(f"✅ Follow-up preserved: {'PASS' if has_follow_up else 'FAIL'}")

def test_storage_consistency():
    """Test that storage maintains follow-up consistency"""
    print("\n💾 Testing Storage Consistency")
    print("=" * 50)

    # Import memory manager
    from memory_manager import get_memory_manager
    memory_manager = get_memory_manager()

    # Test data
    test_query = "What is Donald Trump?"
    test_response = "Donald Trump is the 45th President of the United States.\n\n👉 Would you like to know more about the policies and actions that defined Donald Trump's presidency?"
    test_chat_id = str(ObjectId())
    test_user_id = str(ObjectId())

    print(f"Test query: {test_query}")
    print(f"Test response: {test_response[:50]}...")

    # Extract follow-up before storage
    answer, follow_up_before = memory_manager.extract_follow_up(test_response)
    print(f"Follow-up before storage: {follow_up_before}")

    # Test the extraction consistency
    answer2, follow_up_after = memory_manager.extract_follow_up(test_response)

    consistency_check = follow_up_before == follow_up_after
    print(f"✅ Extraction consistency: {'PASS' if consistency_check else 'FAIL'}")

    if not consistency_check:
        print(f"   First extraction: {follow_up_before}")
        print(f"   Second extraction: {follow_up_after}")

    # Test the new store_interaction method with metadata
    print(f"\n🔍 Testing store_interaction with follow-up metadata...")
    metadata = {
        "mode": "agentic",
        "host": "groq",
        "model": "llama3-8b-8192",
        "follow_up": follow_up_before  # Pass the extracted follow-up
    }

    try:
        # This should use the follow-up from metadata, not re-extract
        memory_manager.store_interaction(test_query, test_response, test_chat_id, test_user_id, metadata)
        print(f"✅ Store interaction completed successfully")

        # Test the new method
        test_message = {
            "id": str(ObjectId()),
            "role": "assistant",
            "content": answer,  # Clean answer without follow-up
            "timestamp": datetime.now(),
            "metadata": {
                "chat_id": test_chat_id,
                "user_id": test_user_id
            }
        }

        returned_follow_up = memory_manager.store_short_term_message_with_follow_up(
            test_user_id, test_chat_id, test_message, follow_up_before
        )

        consistency_check2 = returned_follow_up == follow_up_before
        print(f"✅ New storage method consistency: {'PASS' if consistency_check2 else 'FAIL'}")

        if not consistency_check2:
            print(f"   Expected: {follow_up_before}")
            print(f"   Got: {returned_follow_up}")

    except Exception as e:
        print(f"❌ Storage test failed: {e}")
        import traceback
        traceback.print_exc()

def test_api_response_structure():
    """Test that API response includes follow-up field"""
    print("\n🌐 Testing API Response Structure")
    print("=" * 50)
    
    # Import the QueryResponse model
    from main2 import QueryResponse
    
    # Test creating a QueryResponse with follow-up
    test_response = QueryResponse(
        answer="This is a test answer",
        source="test",
        documents=[],
        source_urls=[],
        chat_id="test_chat_id",
        follow_up="This is a test follow-up question"
    )
    
    # Check if follow_up field exists and is accessible
    has_follow_up_field = hasattr(test_response, 'follow_up')
    follow_up_value = getattr(test_response, 'follow_up', None)
    
    print(f"✅ Follow-up field exists: {'PASS' if has_follow_up_field else 'FAIL'}")
    print(f"✅ Follow-up value correct: {'PASS' if follow_up_value == 'This is a test follow-up question' else 'FAIL'}")
    
    # Test JSON serialization
    try:
        response_dict = test_response.model_dump()
        has_follow_up_in_dict = 'follow_up' in response_dict
        print(f"✅ Follow-up in serialized response: {'PASS' if has_follow_up_in_dict else 'FAIL'}")
        print(f"   Serialized follow-up: {response_dict.get('follow_up')}")
    except Exception as e:
        print(f"❌ Serialization failed: {e}")

def main():
    """Run all tests"""
    print("🚀 Starting Follow-up Question Fix Tests")
    print("=" * 60)
    
    try:
        # Test 1: Follow-up extraction
        test_follow_up_extraction()
        
        # Test 2: Generative model consistency
        asyncio.run(test_generative_model_consistency())
        
        # Test 3: Storage consistency
        test_storage_consistency()
        
        # Test 4: API response structure
        test_api_response_structure()
        
        print("\n" + "=" * 60)
        print("🎉 All tests completed!")
        print("📋 Summary:")
        print("   - Follow-up extraction tested")
        print("   - Generative model consistency verified")
        print("   - Storage consistency checked")
        print("   - API response structure validated")
        print("\n💡 If all tests pass, the follow-up question mismatch issue should be resolved!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
