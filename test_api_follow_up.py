#!/usr/bin/env python3
"""
Test script to verify the API follow-up question fix
Tests the actual API endpoints to ensure follow-up questions are consistent
"""

import requests
import json
import time
from datetime import datetime

def test_api_follow_up():
    """Test the API endpoints for follow-up question consistency"""
    print("🌐 Testing API Follow-up Question Consistency")
    print("=" * 60)
    
    # API configuration
    base_url = "http://localhost:8000"
    
    # Test data
    test_user_id = "507f1f77bcf86cd799439011"  # Valid ObjectId format
    test_query = "What is machine learning?"
    
    # Test payload for query endpoint
    query_payload = {
        "query": test_query,
        "collections": ["Gen AI"],
        "mode": "rag",
        "host": "groq",
        "api_key": "gsk_test_key",  # Replace with actual key if testing
        "model": "llama3-8b-8192",
        "user_id": test_user_id
    }
    
    print(f"Testing query: {test_query}")
    print(f"User ID: {test_user_id}")
    print(f"Mode: {query_payload['mode']}")
    
    try:
        # Test the query endpoint
        print("\n🔍 Testing /query endpoint...")
        response = requests.post(f"{base_url}/query", json=query_payload, timeout=30)
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ Query endpoint successful")
            
            # Check response structure
            required_fields = ['answer', 'source', 'documents', 'source_urls', 'chat_id', 'follow_up']
            missing_fields = [field for field in required_fields if field not in response_data]
            
            if not missing_fields:
                print("✅ All required fields present in response")
                
                # Check follow-up field specifically
                follow_up = response_data.get('follow_up')
                if follow_up:
                    print(f"✅ Follow-up question found: {follow_up}")
                    
                    # Check if follow-up is also in the answer
                    answer = response_data.get('answer', '')
                    follow_up_in_answer = "👉" in answer
                    
                    if follow_up_in_answer:
                        print("✅ Follow-up question also present in answer")
                        
                        # Extract follow-up from answer to compare
                        if "\n\n👉" in answer:
                            answer_parts = answer.split("\n\n👉")
                            if len(answer_parts) > 1:
                                extracted_follow_up = answer_parts[1].strip()
                                consistency_check = extracted_follow_up == follow_up
                                print(f"✅ Follow-up consistency: {'PASS' if consistency_check else 'FAIL'}")
                                
                                if not consistency_check:
                                    print(f"   API follow_up field: {follow_up}")
                                    print(f"   Answer follow_up: {extracted_follow_up}")
                            else:
                                print("⚠️ Follow-up marker found but no content after it")
                        else:
                            print("⚠️ Follow-up field exists but not found in answer format")
                    else:
                        print("⚠️ Follow-up field exists but not in answer text")
                else:
                    print("ℹ️ No follow-up question in response (this is OK for some responses)")
                
                # Print response summary
                print(f"\n📋 Response Summary:")
                print(f"   Answer length: {len(response_data.get('answer', ''))}")
                print(f"   Source: {response_data.get('source')}")
                print(f"   Documents: {len(response_data.get('documents', []))}")
                print(f"   Chat ID: {response_data.get('chat_id')}")
                print(f"   Follow-up: {'Yes' if follow_up else 'No'}")
                
            else:
                print(f"❌ Missing required fields: {missing_fields}")
                
        else:
            print(f"❌ Query endpoint failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure the server is running on localhost:8000")
        print("   Start the server with: uvicorn main2:app --host 0.0.0.0 --port 8000")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timed out - server might be processing")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True

def test_rag_query_endpoint():
    """Test the RAG query endpoint specifically"""
    print("\n🔍 Testing /rag_query endpoint...")
    
    base_url = "http://localhost:8000"
    test_user_id = "507f1f77bcf86cd799439011"
    
    rag_payload = {
        "query": "Explain artificial intelligence",
        "collections": ["Gen AI"],
        "mode": "rag",
        "host": "groq",
        "api_key": "gsk_test_key",
        "model": "llama3-8b-8192",
        "user_id": test_user_id
    }
    
    try:
        response = requests.post(f"{base_url}/rag_query", json=rag_payload, timeout=30)
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ RAG query endpoint successful")
            
            # Check for follow-up field
            follow_up = response_data.get('follow_up')
            has_follow_up_field = 'follow_up' in response_data
            
            print(f"✅ Follow-up field present: {'Yes' if has_follow_up_field else 'No'}")
            if follow_up:
                print(f"✅ Follow-up content: {follow_up}")
            
        else:
            print(f"❌ RAG query endpoint failed with status {response.status_code}")
            
    except Exception as e:
        print(f"❌ RAG query test failed: {e}")

def main():
    """Run the API follow-up tests"""
    print("🚀 Starting API Follow-up Question Tests")
    print("=" * 60)
    
    # Test the main query endpoint
    success = test_api_follow_up()
    
    if success:
        # Test the RAG query endpoint
        test_rag_query_endpoint()
        
        print("\n" + "=" * 60)
        print("🎉 API Follow-up Tests Completed!")
        print("📋 Summary:")
        print("   - Query endpoint tested for follow-up consistency")
        print("   - RAG query endpoint tested for follow-up field")
        print("   - Response structure validated")
        print("\n💡 The follow-up question mismatch should now be fixed!")
        print("   - Same follow-up stored in Redis/MongoDB")
        print("   - Same follow-up returned in API response")
        print("   - Follow-up appears in both answer text and follow_up field")
    else:
        print("\n❌ Tests failed - please check server status and try again")

if __name__ == "__main__":
    main()
