#!/usr/bin/env python3
"""
Test script to verify the <PERSON> follow-up question fix
Simulates the exact scenario described by the user
"""

import json
import logging
from datetime import datetime
from bson.objectid import ObjectId

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DonaldTrumpTest")

def test_donald_trump_scenario():
    """Test the exact Donald Trump scenario described by the user"""
    print("🇺🇸 Testing Donald Trump Follow-up Question Scenario")
    print("=" * 60)
    
    # Import required modules
    from memory_manager import get_memory_manager
    from generative_model import get_generative_model
    
    # Initialize components
    memory_manager = get_memory_manager()
    generative_model = get_generative_model()
    
    # Test data - simulating the exact scenario
    query = "donald trump"
    
    # Simulate LLM response with the follow-up question that should be stored in Redis
    llm_response = """<PERSON> is the 45th President of the United States, serving from January 20, 2017, to January 20, 2021. He was a businessman and television personality before entering politics.

👉 Would you like to know more about the policies and actions that defined <PERSON>'s presidency?"""
    
    test_chat_id = str(ObjectId())
    test_user_id = str(ObjectId())
    
    print(f"Query: {query}")
    print(f"LLM Response: {llm_response[:100]}...")
    print(f"Chat ID: {test_chat_id}")
    print(f"User ID: {test_user_id}")
    
    # Step 1: Test generative model response processing
    print(f"\n🤖 Step 1: Testing Generative Model Processing")
    print("-" * 40)
    
    # Simulate what happens in generative_model.py
    response_dict = {"answer": llm_response, "follow_up": None}
    
    # Extract follow-up from the response
    if "👉" in llm_response:
        parts = llm_response.split("\n\n👉")
        if len(parts) > 1:
            follow_up_from_llm = parts[1].strip()
            response_dict["follow_up"] = follow_up_from_llm
            print(f"✅ Extracted follow-up from LLM: {follow_up_from_llm}")
        else:
            print("❌ Found 👉 but no follow-up content")
    else:
        print("ℹ️ No follow-up marker found in LLM response")
    
    # Step 2: Test memory manager storage
    print(f"\n💾 Step 2: Testing Memory Manager Storage")
    print("-" * 40)
    
    # Simulate the metadata that would be passed
    metadata = {
        "mode": "agentic",
        "host": "groq",
        "model": "llama3-8b-8192",
        "sources": [],
        "source_urls": [],
        "follow_up": response_dict["follow_up"]
    }
    
    print(f"Metadata follow-up: {metadata['follow_up']}")
    
    # Test the store_interaction method
    try:
        memory_manager.store_interaction(
            query=query,
            response=llm_response,
            chat_id=test_chat_id,
            user_id=test_user_id,
            follow_up=response_dict["follow_up"],
            metadata=metadata
        )
        print(f"✅ Successfully stored interaction with follow-up")
    except Exception as e:
        print(f"❌ Failed to store interaction: {e}")
        return False
    
    # Step 3: Test follow-up extraction consistency
    print(f"\n🔍 Step 3: Testing Follow-up Extraction Consistency")
    print("-" * 40)
    
    # Extract follow-up using memory manager method
    answer, extracted_follow_up = memory_manager.extract_follow_up(llm_response)
    
    print(f"Original LLM follow-up: {response_dict['follow_up']}")
    print(f"Memory manager extracted: {extracted_follow_up}")
    
    # Check consistency
    consistency_check = response_dict["follow_up"] == extracted_follow_up
    print(f"✅ Follow-up consistency: {'PASS' if consistency_check else 'FAIL'}")
    
    if not consistency_check:
        print(f"❌ MISMATCH DETECTED!")
        print(f"   LLM follow-up: {response_dict['follow_up']}")
        print(f"   Extracted follow-up: {extracted_follow_up}")
        return False
    
    # Step 4: Test API response structure
    print(f"\n🌐 Step 4: Testing API Response Structure")
    print("-" * 40)
    
    # Simulate what the API would return
    from main2 import QueryResponse
    
    api_response = QueryResponse(
        answer=answer,  # Clean answer without follow-up
        source="qdrant",
        documents=[],
        source_urls=[],
        chat_id=test_chat_id,
        follow_up=response_dict["follow_up"]  # Follow-up in separate field
    )
    
    print(f"API answer length: {len(api_response.answer)}")
    print(f"API follow-up: {api_response.follow_up}")
    
    # Check if the API response contains the same follow-up
    api_consistency = api_response.follow_up == response_dict["follow_up"]
    print(f"✅ API response consistency: {'PASS' if api_consistency else 'FAIL'}")
    
    if not api_consistency:
        print(f"❌ API MISMATCH DETECTED!")
        print(f"   Expected: {response_dict['follow_up']}")
        print(f"   API returned: {api_response.follow_up}")
        return False
    
    # Step 5: Final verification
    print(f"\n✅ Step 5: Final Verification")
    print("-" * 40)
    
    print(f"🎯 EXPECTED BEHAVIOR:")
    print(f"   Redis stores: {response_dict['follow_up']}")
    print(f"   FastAPI shows: {response_dict['follow_up']}")
    print(f"   ✅ Both should be identical!")
    
    print(f"\n🔧 ACTUAL BEHAVIOR:")
    print(f"   Memory manager extracts: {extracted_follow_up}")
    print(f"   API response contains: {api_response.follow_up}")
    
    final_check = (extracted_follow_up == api_response.follow_up == response_dict["follow_up"])
    print(f"   ✅ All three match: {'YES' if final_check else 'NO'}")
    
    return final_check

def main():
    """Run the Donald Trump scenario test"""
    print("🚀 Starting Donald Trump Follow-up Question Fix Test")
    print("=" * 60)
    
    try:
        success = test_donald_trump_scenario()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 SUCCESS! The follow-up question mismatch is FIXED!")
            print("📋 Results:")
            print("   ✅ Same follow-up stored in Redis")
            print("   ✅ Same follow-up returned in FastAPI response")
            print("   ✅ No more mismatch between storage and display")
            print("\n💡 The issue described in the user's query has been resolved!")
        else:
            print("❌ FAILURE! The follow-up question mismatch still exists!")
            print("📋 Issues found:")
            print("   ❌ Different follow-ups in storage vs API response")
            print("   ❌ Inconsistent follow-up extraction")
            print("\n🔧 Additional debugging may be required.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
